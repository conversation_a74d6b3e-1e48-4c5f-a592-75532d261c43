import React, { useState, useEffect } from 'react';
import {
  X,
  Download,
  HardDrive,
  Wifi,
  Bluetooth,
  Calendar,
  FileText,
  Hash,
  ToggleLeft,
  ToggleRight,
  AlertCircle,
  Edit,
  Save,
  XCircle
} from 'lucide-react';
import { Button } from '../ui/button';
import {
  Software,
  updateSoftwareStatus,
  downloadSoftware,
  formatFileSize,
  formatDate,
  updateSoftware,
  checkSoftwareNameExists
} from '../../utils/api/softwareApi';

interface SoftwareDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  software: Software;
  onUpdate: (updatedSoftware?: Software) => void;
}

export function SoftwareDetailModal({ isOpen, onClose, software, onUpdate }: SoftwareDetailModalProps) {
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [checkingName, setCheckingName] = useState(false);
  const [nameExists, setNameExists] = useState(false);
  const [originalName, setOriginalName] = useState('');

  // 當軟體資料變更時，更新編輯表單
  useEffect(() => {
    if (software) {
      setEditName(software.name);
      setEditDescription(software.description || '');
      setOriginalName(software.name);
      setIsEditing(false);
      setNameExists(false);
    }
  }, [software]);

  if (!isOpen) return null;

  // 處理狀態切換
  const handleToggleStatus = async () => {
    try {
      setUpdating(true);
      setError(null);
      await updateSoftwareStatus(software._id, { isEnabled: !software.isEnabled });
      onUpdate();
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新狀態失敗');
    } finally {
      setUpdating(false);
    }
  };

  // 處理下載
  const handleDownload = async (type: 'original' | 'pure') => {
    try {
      setError(null);
      await downloadSoftware(software._id, type);
    } catch (err) {
      setError(err instanceof Error ? err.message : '下載失敗');
    }
  };

  // 檢查軟體名稱是否已存在
  const checkNameExists = async (name: string) => {
    if (!name.trim() || name === originalName) {
      setNameExists(false);
      return;
    }

    try {
      setCheckingName(true);
      const exists = await checkSoftwareNameExists(name.trim());
      setNameExists(exists);
    } catch (err) {
      console.error('檢查軟體名稱失敗:', err);
      setNameExists(false);
    } finally {
      setCheckingName(false);
    }
  };

  // 處理名稱變更
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setEditName(newName);

    // 延遲檢查名稱是否存在
    if (newName.trim() && newName !== originalName) {
      const timeoutId = setTimeout(() => {
        checkNameExists(newName);
      }, 500);

      return () => clearTimeout(timeoutId);
    } else {
      setNameExists(false);
    }
  };

  // 開始編輯
  const startEdit = () => {
    setIsEditing(true);
    setError(null);
  };

  // 取消編輯
  const cancelEdit = () => {
    setIsEditing(false);
    setEditName(software.name);
    setEditDescription(software.description || '');
    setNameExists(false);
    setError(null);
  };

  // 保存編輯
  const saveEdit = async () => {
    if (!editName.trim()) {
      setError('軟體名稱不能為空');
      return;
    }

    if (nameExists) {
      setError('軟體名稱已存在，請使用其他軟體名稱');
      return;
    }

    try {
      setUpdating(true);
      setError(null);

      await updateSoftware(software._id, {
        name: editName.trim(),
        description: editDescription.trim()
      });

      // 更新本地軟體物件以即時反映變更
      const updatedSoftware = {
        ...software,
        name: editName.trim(),
        description: editDescription.trim(),
        lastModified: new Date().toISOString()
      };

      // 更新原始名稱
      setOriginalName(editName.trim());

      setIsEditing(false);

      // 調用父組件的更新回調，並傳遞更新後的軟體物件
      onUpdate(updatedSoftware);
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新失敗');
    } finally {
      setUpdating(false);
    }
  };

  // 獲取設備類型圖標
  const getDeviceIcon = () => {
    switch (software.deviceType) {
      case 'gateway':
        return <HardDrive className="w-5 h-5 text-blue-600" />;
      case 'epd':
        return <HardDrive className="w-5 h-5 text-green-600" />;
      default:
        return <HardDrive className="w-5 h-5 text-gray-600" />;
    }
  };

  // 獲取功能類型圖標
  const getFunctionIcon = () => {
    switch (software.functionType) {
      case 'wifi':
        return <Wifi className="w-5 h-5 text-blue-500" />;
      case 'ble':
        return <Bluetooth className="w-5 h-5 text-blue-500" />;
      default:
        return <HardDrive className="w-5 h-5 text-gray-500" />;
    }
  };

  // 獲取狀態顏色和文字
  const getStatusInfo = () => {
    if (!software.isEnabled) {
      return { color: 'text-gray-500', text: '已禁用', bgColor: 'bg-gray-100' };
    }
    
    switch (software.status) {
      case 'active':
        return { color: 'text-green-600', text: '啟用中', bgColor: 'bg-green-100' };
      case 'deprecated':
        return { color: 'text-yellow-600', text: '已棄用', bgColor: 'bg-yellow-100' };
      default:
        return { color: 'text-gray-500', text: '未知', bgColor: 'bg-gray-100' };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* 標題列 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3 min-w-0 flex-1 pr-4">
            {getDeviceIcon()}
            <div className="min-w-0 flex-1">
              <h2 className="text-xl font-semibold text-gray-900 truncate" title={software.name}>{software.name}</h2>
              <p className="text-sm text-gray-600">版本 {software.version}</p>
            </div>
          </div>
          <div className="flex-shrink-0">
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* 錯誤提示 */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
              <button 
                onClick={() => setError(null)}
                className="ml-auto text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          )}

          {/* 狀態和操作區域 */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
                  {statusInfo.text}
                </span>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  {getFunctionIcon()}
                  <span className="uppercase">{software.functionType}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleToggleStatus}
                  disabled={updating}
                  variant="outline"
                  size="sm"
                  className={software.isEnabled ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
                >
                  {updating ? (
                    <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                  ) : software.isEnabled ? (
                    <ToggleRight className="w-4 h-4 mr-2" />
                  ) : (
                    <ToggleLeft className="w-4 h-4 mr-2" />
                  )}
                  {software.isEnabled ? '禁用' : '啟用'}
                </Button>
                <Button
                  onClick={() => handleDownload('original')}
                  variant="outline"
                  size="sm"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下載原始檔案
                </Button>
                <Button
                  onClick={() => handleDownload('pure')}
                  variant="outline"
                  size="sm"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下載純韌體
                </Button>
              </div>
            </div>
          </div>

          {/* 基本資訊 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">基本資訊</h3>
              
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <FileText className="w-5 h-5 text-gray-400 mt-1" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm text-gray-600">軟體名稱</p>
                      {!isEditing && (
                        <Button
                          onClick={startEdit}
                          variant="outline"
                          size="sm"
                          className="h-6 px-2 text-xs"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          編輯
                        </Button>
                      )}
                    </div>
                    {isEditing ? (
                      <div className="space-y-2">
                        <div className="relative">
                          <input
                            type="text"
                            value={editName}
                            onChange={handleNameChange}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm ${
                              nameExists ? 'border-red-300 bg-red-50' : 'border-gray-300'
                            }`}
                            placeholder="輸入軟體名稱"
                          />
                          {checkingName && (
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                              <div className="animate-spin w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full" />
                            </div>
                          )}
                        </div>
                        {nameExists && (
                          <p className="text-xs text-red-600 flex items-center">
                            <AlertCircle className="w-3 h-3 mr-1" />
                            軟體名稱已存在
                          </p>
                        )}
                      </div>
                    ) : (
                      <p className="font-medium break-words" title={software.name}>{software.name}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Hash className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">韌體版本</p>
                    <p className="font-medium">{software.version}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {getDeviceIcon()}
                  <div>
                    <p className="text-sm text-gray-600">設備類型</p>
                    <p className="font-medium capitalize">{software.deviceType}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <HardDrive className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">裝置型號</p>
                    <p className="font-medium">{software.deviceModelName || `型號 ${software.deviceModel}`}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {getFunctionIcon()}
                  <div>
                    <p className="text-sm text-gray-600">功能類型</p>
                    <p className="font-medium uppercase">{software.functionType}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-600 mb-1">描述</p>
                  {isEditing ? (
                    <textarea
                      value={editDescription}
                      onChange={(e) => setEditDescription(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                      placeholder="輸入軟體描述"
                      rows={3}
                    />
                  ) : (
                    <p className="text-gray-900">{software.description || '無描述'}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">檔案資訊</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">韌體大小</p>
                  <p className="font-medium">{formatFileSize(software.binSize || software.fileSize)}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">CRC校驗和</p>
                  <p className="font-medium font-mono text-sm">{software.checksum}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">最小硬體版本</p>
                  <p className="font-medium">{software.minHwVersion || '未指定'}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">最大硬體版本</p>
                  <p className="font-medium">{software.maxHwVersion || '未指定'}</p>
                </div>

                {software.tags && software.tags.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-600 mb-1">標籤</p>
                    <div className="flex flex-wrap gap-1">
                      {software.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 時間資訊 */}
          <div className="space-y-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">時間資訊</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">上傳時間</p>
                  <p className="font-medium">{formatDate(software.uploadDate)}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">最後修改</p>
                  <p className="font-medium">{formatDate(software.lastModified)}</p>
                </div>
              </div>
            </div>
          </div>


        </div>

        {/* 底部操作區 */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          {isEditing ? (
            <>
              <Button
                onClick={cancelEdit}
                variant="outline"
                disabled={updating}
              >
                <XCircle className="w-4 h-4 mr-2" />
                取消
              </Button>
              <Button
                onClick={saveEdit}
                disabled={updating || !editName.trim() || nameExists || checkingName}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                {updating ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    保存
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button
              onClick={onClose}
              variant="outline"
            >
              關閉
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
